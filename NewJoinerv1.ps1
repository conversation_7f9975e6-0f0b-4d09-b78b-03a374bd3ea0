# Aurora Energy Research - New Joiner Onboarding Script
# Fail-fast version with clear error reporting and interactive repo cloning
# Version: 3.2

Clear-Host

# Enhanced error handling - fail immediately on any critical error
$ErrorActionPreference = "Stop"

# Logging function for clear failure reporting
function Write-FailureAndExit {
    param(
        [Parameter(Mandatory = $true)]
        [string]$FailureReason,
        [string]$Suggestion = "",
        [string]$Phase = "Unknown"
    )
    
    Write-Host ""
    Write-Host "################################################################################" -ForegroundColor Red
    Write-Host "# SETUP FAILED - STOPPING EXECUTION                                              #" -ForegroundColor Red
    Write-Host "################################################################################" -ForegroundColor Red
    Write-Host ""
    Write-Host "Phase: $Phase" -ForegroundColor Yellow
    Write-Host "Failure Reason: $FailureReason" -ForegroundColor Red
    
    if ($Suggestion) {
        Write-Host ""
        Write-Host "Suggested Solution:" -ForegroundColor Cyan
        Write-Host $Suggestion -ForegroundColor White
    }
    
    Write-Host ""
    Write-Host "Please resolve the issue above and run the script again." -ForegroundColor Yellow
    Write-Host "If you need IT support, please provide this exact error message." -ForegroundColor Yellow
    Write-Host ""
    
    # Log to file for reference
    $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') - FAILURE in $Phase`: $FailureReason"
    Add-Content -Path "$env:TEMP\Aurora_Onboarding_Failures.log" -Value $logEntry -ErrorAction SilentlyContinue
    
    Read-Host -Prompt "Press any key to exit..."
    exit 1
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Progress {
    param([string]$Message)
    Write-Host "🔄 $Message" -ForegroundColor Cyan
}

# Function to write phase complete 
function Write-Phase-Complete {
    param (
        [Parameter(Mandatory = $true)]
        $phase
    )

    Write-Host
    Write-Host "################################################################################"
    Write-Host "# Phase $phase Complete.                                                               #" -ForegroundColor Green
    Write-Host "################################################################################"
    Write-Host
    Read-Host -Prompt "Press any key to continue..." | Out-Null
    Write-Host
}

# Function to save script state with error handling
function Save-ScriptState {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Phase,
        [Parameter(Mandatory = $true)]
        [string]$CountryCode,
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath
    )
    
    try {
        $state = @{
            Phase       = $Phase
            CountryCode = $CountryCode
            Timestamp   = Get-Date
        }
        
        $state | ConvertTo-Json | Out-File -FilePath $StateFilePath -Encoding UTF8
        Write-Progress "State saved successfully"
    }
    catch {
        Write-FailureAndExit -FailureReason "Cannot save script state to $StateFilePath" -Suggestion "Check if you have write permissions to the temp directory" -Phase "State Management"
    }
}

# Function to load script state with error handling
function Load-ScriptState {
    param (
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath
    )
    
    if (Test-Path $StateFilePath) {
        try {
            $stateJson = Get-Content $StateFilePath -Raw
            return $stateJson | ConvertFrom-Json
        }
        catch {
            Write-FailureAndExit -FailureReason "Cannot read existing state file" -Suggestion "Delete the file $StateFilePath and restart the script" -Phase "State Management"
        }
    }
    return $null
}

# Function to create directories with proper error handling
function Create-Directories {
    param (
        [Parameter(Mandatory = $true)]
        $Directories
    )

    foreach ($path in $Directories) {
        Write-Progress "Checking directory: $path"
        
        if (!(Test-Path -PathType container $path)) {
            try {
                New-Item -ItemType Directory -Path $path -Force | Out-Null
                Write-Success "Created directory: $path"
            }
            catch {
                Write-FailureAndExit -FailureReason "Cannot create required directory: $path" -Suggestion "Check if you have administrator privileges or the path is valid" -Phase "Directory Creation"
            }
        }
        else {
            Write-Success "Directory exists: $path"
        }
    }
}

# Function to get country-specific configuration
function Get-CountryConfig {
    param (
        [Parameter(Mandatory = $true)]
        [string]$CountryCode
    )
    
    # Master configuration for all countries
    # NOTE: NodeJS and Daff installation have been removed.
    $countryConfigs = @{
        "AUS" = @{
            Name         = "Australia"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift",
                "VSCode", "PowerBI", "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "AER-ENMLF.ppk"
        }
        "DEU" = @{
            Name         = "Germany"
            Apps         = @("Git", "Sourcetree", "SQLWorkbench", "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI", "ThinkCell")
            PuttyKeyFile = "PLACEHOLDER-GERMANY.ppk"
        }
        "GBR" = @{
            Name         = "United Kingdom"
            Apps         = @("Git", "Sourcetree", "SQLWorkbench", "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI", "QGIS", "ThinkCell")
            PuttyKeyFile = "PLACEHOLDER-UK.ppk"
        }
        "BRA" = @{
            Name         = "Brazil"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode",
                "PowerBI", "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-BRAZIL.ppk"
        }
        "IND" = @{
            Name         = "India"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode",
                "PowerBI", "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-INDIA.ppk"
        }
        "USA" = @{
            Name         = "United States"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode",
                "PowerBI", "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-USA.ppk"
        }
        "GRE" = @{
            Name         = "Greece"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI",
                "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-GREECE.ppk"
        }
        "ESP" = @{
            Name         = "Spain"
            Apps         = @("Git", "Sourcetree", "SQLWorkbench", "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI", "ThinkCell")
            PuttyKeyFile = "PLACEHOLDER-SPAIN.ppk"
        }
        "ITA" = @{
            Name         = "Italy"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI",
                "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-ITALY.ppk"
        }
        "SIN" = @{
            Name         = "Singapore"
            Apps         = @("Git", "Sourcetree", "SQLWorkbench", "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI", "ThinkCell")
            PuttyKeyFile = "PLACEHOLDER-SINGAPORE.ppk"
        }
        "FRA" = @{
            Name         = "France"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI",
                "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-FRANCE.ppk"
        }
        "JPN" = @{
            Name         = "Japan"
            Apps         = @("Git", "Sourcetree", "SQLWorkbench", "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI", "ThinkCell")
            PuttyKeyFile = "PLACEHOLDER-JAPAN.ppk"
        }
        "SWE" = @{
            Name         = "Sweden"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI",
                "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-SWEDEN.ppk"
        }
        "CHI" = @{
            Name         = "Chile"
            Apps         = @(
                "Git", "Sourcetree", "PuTTY", "WinSCP", "SQLWorkbench", "R", "RStudio",
                "Python38", "Python312", "PyCharm", "Redshift", "VSCode", "PowerBI",
                "QGIS", "ThinkCell"
            )
            PuttyKeyFile = "PLACEHOLDER-CHILE.ppk"
        }
    }
    
    if (-not $countryConfigs.ContainsKey($CountryCode)) {
        Write-FailureAndExit -FailureReason "Invalid country code: $CountryCode" -Suggestion "Use one of: AUS, DEU, GBR, BRA, IND, USA, GRE, ESP, ITA, SIN, FRA, JPN, SWE, CHI" -Phase "Country Configuration"
    }
    
    return $countryConfigs[$CountryCode]
}

# Function to get application URLs
function Get-ApplicationUrls {
    return @{
        "Git"          = "companyportal:ApplicationId=046d5e09-de15-46cc-9382-ca0b385c4f9d"
        "Sourcetree"   = "companyportal:ApplicationId=da4ceb74-9881-43d6-8b43-7ddd863e9e00"
        "Python38"     = "companyportal:ApplicationId=f3cc6d67-95bf-446e-9164-62dde8ce6eca"
        "Python312"    = "companyportal:ApplicationId=d4e36704-2c6c-4b69-83c6-5eb525c00d3f"
        "PyCharm"      = "companyportal:ApplicationId=c0d06534-4326-48ed-9370-05a48a6ac046"
        "VSCode"       = "companyportal:ApplicationId=a7ae3445-b9a7-498f-8f81-21dacfc50510"
        "PuTTY"        = "companyportal:ApplicationId=b55b70c3-c3b5-4b69-998a-7e313829fb67"
        "WinSCP"       = "companyportal:ApplicationId=40c72b1d-a9aa-40f8-a3b9-620e0e77871d"
        "SQLWorkbench" = "companyportal:ApplicationId=f0ae5888-8cd8-493e-a20e-a45af28db15f"
        "Redshift"     = "companyportal:ApplicationId=9515084f-bee7-4e86-becc-d74e025c291b"
        "R"            = "companyportal:ApplicationId=9d11d46b-d144-489a-883b-6654a1d94f05"
        "RStudio"      = "companyportal:ApplicationId=a60814af-6742-4415-ace7-1ca88b8f011e"
        "PowerBI"      = "companyportal:ApplicationId=a8d6740b-79b7-47e1-afc1-874db09e3767"
        "QGIS"         = "companyportal:ApplicationId=428d1671-bb17-4b87-8953-16cd7a1ee816"
        "ThinkCell"    = "PLACEHOLDER-THINKCELL-COMPANY-PORTAL-URL"
    }
}

# Function to attempt auto-clicking Install button in Company Portal
function Invoke-CompanyPortalAutoInstall {
    param (
        [Parameter(Mandatory = $true)]
        [string]$AppName
    )

    Write-Progress "Attempting to auto-install $AppName using Ctrl+I shortcut..."

    try {
        # Wait for Company Portal to fully load - extended for reliability
        Write-Progress "Waiting for Company Portal to load completely (10 seconds)..."
        Start-Sleep -Seconds 10

        if ($AppName -eq "PowerBI") {
            Write-Progress "Adding extra 10-second delay for PowerBI to ensure it loads fully..."
            Start-Sleep -Seconds 10
        }

        # Retry loop to find and focus the Company Portal window
        Write-Progress "Searching for '$AppName' window to auto-install..."
        $processFound = $false
        $companyPortalProcess = $null
        $maxRetries = 5
        $retryInterval = 3 # seconds

        for ($i = 1; $i -le $maxRetries; $i++) {
            $companyPortalProcess = Get-Process | Where-Object {
                $_.MainWindowTitle -ne "" -and (
                    $_.ProcessName -like "*CompanyPortal*" -or
                    $_.MainWindowTitle -like "*Company Portal*" -or
                    $_.MainWindowTitle -like "*Microsoft Store*" -or
                    $_.ProcessName -like "*WinStore*" -or
                    $_.MainWindowTitle -like "*$AppName*"
                )
            }

            if ($companyPortalProcess) {
                Write-Success "Found window for $AppName (Attempt $i of $maxRetries)"
                $processFound = $true
                break
            }
            
            if ($i -lt $maxRetries) {
                Write-Host "Window for $AppName not found, retrying in $retryInterval seconds..." -ForegroundColor Gray
                Start-Sleep -Seconds $retryInterval
            }
        }

        if ($processFound) {
            # Bring Company Portal to foreground

            [Win32]::ShowWindow($companyPortalProcess[0].MainWindowHandle, 9) # SW_RESTORE
            [Win32]::SetForegroundWindow($companyPortalProcess[0].MainWindowHandle)

            if ($AppName -eq "PowerBI") {
                Write-Progress "Adding extra 7-second delay for PowerBI to ensure install button is ready..."
                Start-Sleep -Seconds 7
            }
            else {
                Start-Sleep -Seconds 3 # Standard pause after focusing
            }

            Write-Success "Company Portal window focused for $AppName"

            # Use the Ctrl+I shortcut to install
            Write-Progress "Sending Ctrl+I to install $AppName..."
            [System.Windows.Forms.SendKeys]::SendWait("^i") # Ctrl+I

            Write-Success "✅ Sent Ctrl+I shortcut to install $AppName!"
            Write-Host "Installation should have started automatically." -ForegroundColor Green
            Start-Sleep -Seconds 3 # Brief pause to let the installation begin
            return $true
        }
        else {
            Write-Host "Could not find Company Portal process for '$AppName' after $maxRetries attempts." -ForegroundColor Yellow
            return $false
        }
    }
    catch {
        Write-Host "Ctrl+I automation failed: $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to get dependency-ordered applications
function Get-DependencyOrderedApps {
    param (
        [Parameter(Mandatory = $true)]
        [array]$AppList,
        [Parameter(Mandatory = $true)]
        [hashtable]$AppUrls
    )
    
    # Define dependency relationships (prerequisite -> dependent)
    $dependencies = @{
        "Git"          = @() # Git has no dependencies, install first
        "R"            = @() # R for Windows has no dependencies
        "RStudio"      = @("R") # RStudio requires R for Windows
        "Python38"     = @()
        "Python312"    = @()
        "PyCharm"      = @("Python38", "Python312") # PyCharm should come after Python
        "VSCode"       = @()
        "Sourcetree"   = @("Git") # Sourcetree requires Git
        "PuTTY"        = @()
        "WinSCP"       = @()
        "SQLWorkbench" = @()
        "Redshift"     = @()
        "PowerBI"      = @()
        "QGIS"         = @()
        "ThinkCell"    = @()
    }

    # Create ordered list respecting dependencies
    $orderedApps = @()
    $remaining = $AppList | Where-Object { $AppUrls.ContainsKey($_) }
    $processed = @()

    # Keep processing until all apps are ordered
    $maxIterations = 50 # Prevent infinite loops
    $iteration = 0

    while ($remaining.Count -gt 0 -and $iteration -lt $maxIterations) {
        $iteration++
        $addedThisRound = @()

        foreach ($app in $remaining) {
            $appDependencies = $dependencies[$app]

            if (-not $appDependencies) {
                # No dependencies defined - can install anytime
                $orderedApps += $app
                $addedThisRound += $app
                $processed += $app
            }
            else {
                # Check if all dependencies are satisfied
                $dependenciesMet = $true
                foreach ($dependency in $appDependencies) {
                    # Check if dependency is in our app list and not yet processed
                    if ($AppList -contains $dependency -and $processed -notcontains $dependency) {
                        $dependenciesMet = $false
                        break
                    }
                }

                if ($dependenciesMet) {
                    $orderedApps += $app
                    $addedThisRound += $app
                    $processed += $app
                }
            }
        }

        # Remove processed apps from remaining
        $remaining = $remaining | Where-Object { $addedThisRound -notcontains $_ }

        # If we didn't add anything this round, add remaining apps anyway to prevent infinite loop
        if ($addedThisRound.Count -eq 0 -and $remaining.Count -gt 0) {
            Write-Host "Warning: Circular dependencies detected, adding remaining apps: $($remaining -join ', ')" -ForegroundColor Yellow
            $orderedApps += $remaining
            break
        }
    }

    Write-Host "Application installation order (respecting dependencies):" -ForegroundColor Cyan
    for ($i = 0; $i -lt $orderedApps.Count; $i++) {
        $depInfo = if ($dependencies[$orderedApps[$i]]) { " (requires: $($dependencies[$orderedApps[$i]] -join ', '))" } else { "" }
        Write-Host "  $($i + 1). $($orderedApps[$i])$depInfo" -ForegroundColor White
    }
    Write-Host ""

    return $orderedApps
}
# Enhanced function to install and verify applications with auto-click and polling
function Install-RequiredApplications {
    param (
        [Parameter(Mandatory = $true)]
        [hashtable]$AppListWithUrls
    )

    Write-Progress "Installing and verifying required applications in dependency order..."

    # Get dependency-ordered list of applications
    $appNames = $AppListWithUrls.Keys
    $orderedApps = Get-DependencyOrderedApps -AppList $appNames -AppUrls $AppListWithUrls

    foreach ($appName in $orderedApps) {
        $appUrl = $AppListWithUrls[$appName]

        # Check if app is already installed
        $appFound = Test-SingleApplication -AppName $appName

        if ($appFound) {
            Write-Success "✅ $appName is already installed"
            continue
        }

        # App not found - launch Company Portal to install it
        Write-Host ""
        Write-Host "📦 Installing: $appName" -ForegroundColor Cyan
        Write-Host "Opening Company Portal for $appName..."

        try {
            # Try multiple methods to open Company Portal
            $openSuccess = $false

            # Method 1: Direct Start-Process
            try {
                Start-Process $appUrl -ErrorAction Stop
                Start-Sleep -Seconds 2
                $openSuccess = $true
                Write-Success "Company Portal opened for $appName (Method 1)"
            }
            catch {
                Write-Host "Method 1 failed: $($_.Exception.Message)" -ForegroundColor Yellow
            }
            
            # Method 2: Use cmd /c start if Method 1 failed
            if (-not $openSuccess) {
                try {
                    cmd /c start "`"$appUrl`""
                    Start-Sleep -Seconds 2
                    $openSuccess = $true
                    Write-Success "Company Portal opened for $appName (Method 2)"
                }
                catch {
                    Write-Host "Method 2 failed: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }

            # Method 3: Use Invoke-Expression if Method 2 failed
            if (-not $openSuccess) {
                try {
                    Invoke-Expression "& `"$appUrl`""
                    Start-Sleep -Seconds 2
                    $openSuccess = $true
                    Write-Success "Company Portal opened for $appName (Method 3)"
                }
                catch {
                    Write-Host "Method 3 failed: $($_.Exception.Message)" -ForegroundColor Yellow
                }
            }

            # Method 4: Manual fallback instructions
            if (-not $openSuccess) {
                Write-Host ""
                Write-Host "⚠️  Automatic Company Portal launch failed. Please open manually:" -ForegroundColor Yellow
                Write-Host "1. Open Company Portal app from Start Menu" -ForegroundColor White
                Write-Host "2. Search for '$appName'" -ForegroundColor White
                Write-Host "3. Or copy this URL and paste in Run dialog (Windows+R):" -ForegroundColor White
                Write-Host "   $appUrl" -ForegroundColor Cyan
                Write-Host ""

                $manualOpen = Read-Host -Prompt "Press Enter AFTER you have opened Company Portal and navigated to $appName"
                Write-Success "User confirmed Company Portal is open for $appName"
            }
        }
        catch {
            Write-FailureAndExit -FailureReason "Cannot open Company Portal for $appName using any method" -Suggestion "Manually open Company Portal app and search for '$appName'. If Company Portal app is not installed, install it from Microsoft Store. URL attempted: $appUrl" -Phase "Application Installation"
        }

        # Attempt auto-clicking the Install button
        $autoClickSuccess = Invoke-CompanyPortalAutoInstall -AppName $appName

        if ($autoClickSuccess) {
            Write-Host "✅ Auto-sent Ctrl+I for $appName" -ForegroundColor Green
            Write-Host "🔄 Monitoring installation progress..." -ForegroundColor Cyan

            # Determine appropriate timeout based on application type
            $maxPollAttempts = switch ($appName) {
                "R" { 120 } # R for Windows can be large - 20 minutes
                "RStudio" { 120 } # RStudio is also large - 20 minutes
                "PyCharm" { 120 } # PyCharm is very large - 20 minutes
                "VSCode" { 90 }  # VSCode is moderately large - 15 minutes
                "Git" { 60 }  # Git is medium - 10 minutes
                "SQLWorkbench" { 90 }  # SQL Workbench can be large - 15 minutes
                "QGIS" { 120 } # QGIS is very large - 20 minutes
                default { 60 }  # Default 10 minutes for other apps
            }

            $pollIntervalSeconds = 10
            Write-Host "Timeout set to $($maxPollAttempts * $pollIntervalSeconds / 60) minutes for $appName" -ForegroundColor Gray

            # Start polling for installation completion
            $pollAttempts = 0

            do {
                Start-Sleep -Seconds $pollIntervalSeconds
                $pollAttempts++

                if ($pollAttempts % 6 -eq 0) {
                    # Show progress every minute
                    $minutesElapsed = [math]::Round($pollAttempts * $pollIntervalSeconds / 60, 1)
                    $maxMinutes = [math]::Round($maxPollAttempts * $pollIntervalSeconds / 60, 1)
                    Write-Host "⏱️  Installation in progress... ($minutesElapsed of $maxMinutes minutes)" -ForegroundColor Gray
                }

                $appFound = Test-SingleApplication -AppName $appName

                if ($appFound) {
                    Write-Success "🎉 $appName installation detected! Moving to next application."
                    break
                }

            } while ($pollAttempts -lt $maxPollAttempts -and -not $appFound)

            # Final check after polling
            if (-not $appFound) {
                Write-Host ""
                Write-Host "⚠️  Auto-installation polling timed out for $appName" -ForegroundColor Yellow
                Write-Host "This may be normal for large applications that take longer to install." -ForegroundColor Yellow
                $manualConfirm = Read-Host -Prompt "Has $appName finished installing? Check Company Portal for completion status. (y/n)"

                if ($manualConfirm -eq 'n' -or $manualConfirm -eq 'N') {
                    Write-FailureAndExit -FailureReason "$appName installation was not completed" -Suggestion "Wait for $appName installation to complete in Company Portal and run this script again. Large applications like R, RStudio, PyCharm may take 15-30 minutes." -Phase "Application Installation"
                }
                else {
                    Write-Success "✅ User confirmed $appName installation completed"
                }
            }
        }
        else {
            # Auto-click failed, fall back to manual process with extended timeout
            Write-Host "⚠️  Could not auto-send Ctrl+I. Please install manually." -ForegroundColor Yellow
            Write-Host ""
            Write-Host "Manual installation steps for $appName" -ForegroundColor Yellow
            Write-Host "1. Company Portal should be open with $appName displayed" -ForegroundColor White
            Write-Host "2. Click the 'Install' button for $appName (or press Ctrl+I)" -ForegroundColor White
            Write-Host "3. Wait for installation to complete" -ForegroundColor White
            Write-Host "4. Installation will be automatically detected" -ForegroundColor White
            Write-Host ""
            Write-Host "🔄 Monitoring for installation completion..." -ForegroundColor Cyan

            # Extended timeout for manual installations
            $maxPollAttempts = switch ($appName) {
                "R" { 180 } # 30 minutes for manual R installation
                "RStudio" { 180 } # 30 minutes for manual RStudio
                "PyCharm" { 180 } # 30 minutes for manual PyCharm
                "VSCode" { 120 } # 20 minutes for manual VSCode
                "QGIS" { 180 } # 30 minutes for manual QGIS
                default { 120 } # 20 minutes default for manual
            }

            $pollIntervalSeconds = 5 # Check more frequently for manual installs
            Write-Host "Extended timeout: $($maxPollAttempts * $pollIntervalSeconds / 60) minutes for manual installation" -ForegroundColor Gray

            # Start polling for manual installation
            $pollAttempts = 0

            do {
                Start-Sleep -Seconds $pollIntervalSeconds
                $pollAttempts++

                if ($pollAttempts % 12 -eq 0) {
                    # Show message every minute
                    $minutesElapsed = [math]::Round($pollAttempts * $pollIntervalSeconds / 60, 1)
                    $maxMinutes = [math]::Round($maxPollAttempts * $pollIntervalSeconds / 60, 1)
                    Write-Host "⏱️  Still waiting for $appName installation... ($minutesElapsed of $maxMinutes minutes)" -ForegroundColor Gray
                }

                $appFound = Test-SingleApplication -AppName $appName

                if ($appFound) {
                    Write-Success "🎉 $appName installation detected! Moving to next application."
                    break
                }

            } while ($pollAttempts -lt $maxPollAttempts -and -not $appFound)

            # Final check for manual installation
            if (-not $appFound) {
                Write-FailureAndExit -FailureReason "$appName installation not detected after extended waiting period" -Suggestion "Verify $appName installation completed in Company Portal. For large applications, you may need to wait longer or restart and run this script again." -Phase "Application Installation"
            }
        }
    }

    Write-Success "🎉 All required applications are installed successfully!"
}

# Helper function to check if a single application is installed
function Test-SingleApplication {
    param (
        [Parameter(Mandatory = $true)]
        [string]$AppName
    )

    try {
        # Get installed applications
        $allModernApps = Get-AppxPackage |
        Where-Object { -not ($_.Name -like "Microsoft.*" -or $_.Publisher -like "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US") } |
        Select-Object -ExpandProperty Name
        $allTraditionalAppsHKLM = Get-ItemProperty HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\* -ErrorAction SilentlyContinue |
        Where-Object { $_.DisplayName -ne $null } |
        Select-Object -ExpandProperty DisplayName
        $allTraditionalAppsWow64 = Get-ItemProperty HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\* -ErrorAction SilentlyContinue |
        Where-Object { $_.DisplayName -ne $null } |
        Select-Object -ExpandProperty DisplayName
        $allTraditionalAppsHKCU = Get-ItemProperty HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\* -ErrorAction SilentlyContinue |
        Where-Object { $_.DisplayName -ne $null } |
        Select-Object -ExpandProperty DisplayName
        $allTraditionalApps = ($allTraditionalAppsHKLM + $allTraditionalAppsWow64 + $allTraditionalAppsHKCU) | Sort-Object -Unique

        # Enhanced detection with multiple possible names and command checks
        switch ($AppName) {
            "Sourcetree" {
                Write-Host "  Checking for Sourcetree installation..." -ForegroundColor Gray

                # Method 1: Check registry for various Sourcetree names
                $sourcetreePatterns = @("*Sourcetree*", "*SourceTree*")
                foreach ($pattern in $sourcetreePatterns) {
                    $sourcetreeMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($sourcetreeMatch) {
                        Write-Host "  ✅ Found Sourcetree in registry: $($sourcetreeMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }

                # Method 2: Check for Sourcetree installation paths
                $sourcetreePaths = @(
                    "$env:LOCALAPPDATA\Sourcetree",
                    "$env:ProgramFiles\Atlassian\Sourcetree",
                    "$env:ProgramFiles(x86)\Atlassian\Sourcetree",
                    "C:\Program Files (x86)\Atlassian\Sourcetree Enterprise\SourceTree.exe" # Enterprise location
                )

                foreach ($sourcetreePath in $sourcetreePaths) {
                    if (Test-Path "$sourcetreePath\SourceTree.exe" -or (Test-Path $sourcetreePath)) {
                        Write-Host "  ✅ Found Sourcetree executable at: $sourcetreePath" -ForegroundColor Green
                        return $true
                    }
                }
                
                Write-Host "  ❌ Sourcetree not detected by any method" -ForegroundColor Red
                return $false
            }
            "Git" {
                # Check for git command first
                $gitCommand = Get-Command git -ErrorAction SilentlyContinue
                if ($gitCommand) { 
                    Write-Host "  ✅ Found git command at: $($gitCommand.Source)" -ForegroundColor Green
                    return $true 
                }

                # Check for Git in registry
                $gitMatch = $allTraditionalApps | Where-Object { $_ -like "*Git*" }
                if ($gitMatch) { 
                    Write-Host "  ✅ Found Git in registry: $($gitMatch -join ', ')" -ForegroundColor Green
                    return $true 
                }
                
                Write-Host "  ❌ Git not detected" -ForegroundColor Red
                return $false
            }
            { $_ -in @("Python38", "Python310", "Python312", "Python313") } {
                Write-Host "  Checking for Python installation..." -ForegroundColor Gray

                # Method 1: Check for python command and verify it's working
                $pythonCommand = Get-Command python -ErrorAction SilentlyContinue
                if ($pythonCommand) {
                    # Make sure it's not a Windows Store alias
                    if ($pythonCommand.Source -notlike "*WindowsApps*") {
                        try {
                            $pythonVersion = python --version 2>&1
                            if ($LASTEXITCODE -eq 0) {
                                Write-Host "  ✅ Found working python command: $pythonVersion at $($pythonCommand.Source)" -ForegroundColor Green
                                return $true
                            }
                        }
                        catch {
                            # Python command exists but doesn't work
                        }
                    }
                }

                # Method 2: Check for Python executables in common installation paths
                $pythonPaths = @(
                    "$env:LOCALAPPDATA\Programs\Python\Python*\python.exe",
                    "$env:PROGRAMFILES\Python*\python.exe",
                    "$env:PROGRAMFILES(x86)\Python*\python.exe",
                    "C:\Python*\python.exe"
                )

                foreach ($path in $pythonPaths) {
                    $foundPython = Get-ChildItem $path -ErrorAction SilentlyContinue
                    if ($foundPython) {
                        Write-Host "  ✅ Found Python executable at: $($foundPython.FullName)" -ForegroundColor Green
                        return $true
                    }
                }

                # Method 3: Check for specific Python versions in registry (more strict)
                $pythonPatterns = @("Python 3.*", "Python 2.*")
                foreach ($pattern in $pythonPatterns) {
                    $pythonMatch = $allTraditionalApps | Where-Object { $_ -like $pattern -and $_ -notlike "*Launcher*" }
                    if ($pythonMatch) {
                        Write-Host "  ✅ Found Python in registry: $($pythonMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }

                Write-Host "  ❌ Python not detected by any method" -ForegroundColor Red
                return $false
            }

            "R" {
                # Check for R command
                $rCommand = Get-Command R -ErrorAction SilentlyContinue
                if ($rCommand) { 
                    Write-Host "  ✅ Found R command at: $($rCommand.Source)" -ForegroundColor Green
                    return $true 
                }

                # Check for R in registry
                $rMatch = $allTraditionalApps | Where-Object { $_ -like "*R for Windows*" -or $_ -like "*R-*" -or ($_ -like "*R *" -and $_ -notlike "*Repair*" -and $_ -notlike "*Remove*") }
                if ($rMatch) { 
                    Write-Host "  ✅ Found R in registry: $($rMatch -join ', ')" -ForegroundColor Green
                    return $true 
                }
                
                Write-Host "  ❌ R not detected" -ForegroundColor Red
                return $false
            }

            "RStudio" {
                # Check for RStudio in registry
                $rstudioMatch = $allTraditionalApps | Where-Object { $_ -like "*RStudio*" -or $_ -like "*R Studio*" }
                if ($rstudioMatch) { 
                    Write-Host "  ✅ Found RStudio in registry: $($rstudioMatch -join ', ')" -ForegroundColor Green
                    return $true 
                }
                
                Write-Host "  ❌ RStudio not detected" -ForegroundColor Red
                return $false
            }

            "VSCode" {
                Write-Host "  Checking for VS Code installation..." -ForegroundColor Gray

                # Method 1: Check for code command
                $codeCommand = Get-Command code -ErrorAction SilentlyContinue
                if ($codeCommand) {
                    Write-Host "  ✅ Found 'code' command at: $($codeCommand.Source)" -ForegroundColor Green
                    return $true
                }

                # Method 2: Check registry for various VS Code names
                $vscodePatterns = @("*Visual Studio Code*", "*VSCode*", "*VS Code*", "*Code*")
                foreach ($pattern in $vscodePatterns) {
                    $vscodeMatch = $allTraditionalApps | Where-Object { $_ -like $pattern -and $_ -notlike "*Visual Studio 20*" -and $_ -notlike "*Visual Studio Community*" -and $_ -notlike "*Visual Studio Professional*" }
                    if ($vscodeMatch) {
                        Write-Host "  ✅ Found VS Code in registry: $($vscodeMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }

                # Method 3: Check for VS Code installation paths
                $vscodePaths = @(
                    "$env:LOCALAPPDATA\Programs\Microsoft VS Code",
                    "$env:ProgramFiles\Microsoft VS Code",
                    "$env:ProgramFiles(x86)\Microsoft VS Code",
                    "$env:USERPROFILE\AppData\Local\Programs\Microsoft VS Code"
                )

                foreach ($vscodePath in $vscodePaths) {
                    if (Test-Path "$vscodePath\Code.exe") {
                        Write-Host "  ✅ Found VS Code executable at: $vscodePath\Code.exe" -ForegroundColor Green
                        return $true
                    }
                }

                # Method 4: Check AppX packages for VS Code
                $vscodeAppX = $allModernApps | Where-Object { $_ -like "*VisualStudioCode*" -or $_ -like "*VSCode*" }
                if ($vscodeAppX) {
                    Write-Host "  ✅ Found VS Code as modern app: $($vscodeAppX -join ', ')" -ForegroundColor Green
                    return $true
                }

                Write-Host "  ❌ VS Code not detected by any method" -ForegroundColor Red
                return $false
            }
            "SQLWorkbench" {
                Write-Host "  Checking for SQL Workbench installation..." -ForegroundColor Gray

                # Method 1: Check for common SQL Workbench registry names
                $sqlwbPatterns = @("*SQL Workbench*", "*SQLWorkbench*", "*SQL-Workbench*", "*SqlWorkbench*", "*SQL Workbench/J*", "*SQLWorkbench/J*")
                foreach ($pattern in $sqlwbPatterns) {
                    $sqlwbMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($sqlwbMatch) {
                        Write-Host "  ✅ Found SQL Workbench in registry: $($sqlwbMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }

                # Method 2: Check for SQL Workbench installation paths
                $sqlwbPaths = @(
                    "C:\SQLWorkbench\SQLWorkbench64.exe",
                    "C:\SQLWorkbench_AER\SQLWorkbench64.exe",
                    "$env:ProgramFiles\SQLWorkbench*",
                    "$env:ProgramFiles(x86)\SQLWorkbench*",
                    "$env:LOCALAPPDATA\Programs\SQLWorkbench*"
                )

                foreach ($sqlwbPath in $sqlwbPaths) {
                    if (Test-Path $sqlwbPath) {
                        Write-Host "  ✅ Found SQL Workbench at: $sqlwbPath" -ForegroundColor Green
                        return $true
                    }
                }

                Write-Host "  ❌ SQL Workbench not detected by any method" -ForegroundColor Red
                return $false
            }
            "PuTTY" {
                Write-Host "  Checking for PuTTY installation..." -ForegroundColor Gray
                $puttyPatterns = @("*PuTTY*", "*putty*")
                foreach ($pattern in $puttyPatterns) {
                    $puttyMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($puttyMatch) {
                        Write-Host "  ✅ Found PuTTY in registry: $($puttyMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ PuTTY not detected" -ForegroundColor Red
                return $false
            }
            
            "WinSCP" {
                Write-Host "  Checking for WinSCP installation..." -ForegroundColor Gray
                $winscpPatterns = @("*WinSCP*", "*winscp*")
                foreach ($pattern in $winscpPatterns) {
                    $winscpMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($winscpMatch) {
                        Write-Host "  ✅ Found WinSCP in registry: $($winscpMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ WinSCP not detected" -ForegroundColor Red
                return $false
            }

            "PowerBI" {
                Write-Host "  Checking for Power BI installation..." -ForegroundColor Gray
                $powerbiPatterns = @("*Power BI*", "*PowerBI*", "*Microsoft Power BI*")
                foreach ($pattern in $powerbiPatterns) {
                    $powerbiMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($powerbiMatch) {
                        Write-Host "  ✅ Found Power BI in registry: $($powerbiMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ Power BI not detected" -ForegroundColor Red
                return $false
            }

            "PyCharm" {
                Write-Host "  Checking for PyCharm installation..." -ForegroundColor Gray
                $pycharmPatterns = @("*PyCharm*", "*JetBrains PyCharm*", "*pycharm*")
                foreach ($pattern in $pycharmPatterns) {
                    $pycharmMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($pycharmMatch) {
                        Write-Host "  ✅ Found PyCharm in registry: $($pycharmMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ PyCharm not detected" -ForegroundColor Red
                return $false
            }

            "Redshift" {
                Write-Host "  Checking for Amazon Redshift ODBC Driver..." -ForegroundColor Gray
                $redshiftPatterns = @("*Redshift*", "*Amazon Redshift*", "*redshift*")
                foreach ($pattern in $redshiftPatterns) {
                    $redshiftMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($redshiftMatch) {
                        Write-Host "  ✅ Found Redshift ODBC Driver in registry: $($redshiftMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ Redshift ODBC Driver not detected" -ForegroundColor Red
                return $false
            }

            "QGIS" {
                Write-Host "  Checking for QGIS installation..." -ForegroundColor Gray
                $qgisPatterns = @("*QGIS*", "*qgis*")
                foreach ($pattern in $qgisPatterns) {
                    $qgisMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($qgisMatch) {
                        Write-Host "  ✅ Found QGIS in registry: $($qgisMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ QGIS not detected" -ForegroundColor Red
                return $false
            }

            "ThinkCell" {
                Write-Host "  Checking for think-cell installation..." -ForegroundColor Gray
                $thinkcellPatterns = @("*think-cell*", "*ThinkCell*", "*Think-Cell*", "*thinkcell*")
                foreach ($pattern in $thinkcellPatterns) {
                    $thinkcellMatch = $allTraditionalApps | Where-Object { $_ -like $pattern }
                    if ($thinkcellMatch) {
                        Write-Host "  ✅ Found think-cell in registry: $($thinkcellMatch -join ', ')" -ForegroundColor Green
                        return $true
                    }
                }
                Write-Host "  ❌ think-cell not detected" -ForegroundColor Red
                return $false
            }

        }

        return $false
    }
    catch {
        Write-Host "Warning: Could not check installation status for $AppName - $($_.Exception.Message)" -ForegroundColor Yellow
        return $false
    }
}

# Function to select country with validation
function Select-Country {
    Write-Host "Please select your country/region:"
    Write-Host ""

    $countries = @(
        @{ Code = "AUS"; Name = "Australia" },
        @{ Code = "DEU"; Name = "Germany" },
        @{ Code = "GBR"; Name = "United Kingdom" },
        @{ Code = "BRA"; Name = "Brazil" },
        @{ Code = "IND"; Name = "India" },
        @{ Code = "USA"; Name = "United States" },
        @{ Code = "GRE"; Name = "Greece" },
        @{ Code = "ESP"; Name = "Spain" },
        @{ Code = "ITA"; Name = "Italy" },
        @{ Code = "SIN"; Name = "Singapore" },
        @{ Code = "FRA"; Name = "France" },
        @{ Code = "JPN"; Name = "Japan" },
        @{ Code = "SWE"; Name = "Sweden" },
        @{ Code = "CHI"; Name = "Chile" }
    )

    for ($i = 0; $i -lt $countries.Length; $i++) {
        Write-Host "$($i + 1). $($countries[$i].Name) ($($countries[$i].Code))"
    }

    Write-Host ""
    do {
        $selection = Read-Host -Prompt "Enter the number corresponding to your country (1-$($countries.Length))"
        $selectionNum = $selection -as [int]

        if ($selectionNum -lt 1 -or $selectionNum -gt $countries.Length) {
            Write-Host "Invalid selection. Please enter a number between 1 and $($countries.Length)" -ForegroundColor Red
        }
    } while ($selectionNum -lt 1 -or $selectionNum -gt $countries.Length)

    $selectedCountry = $countries[$selectionNum - 1]
    Write-Host "Selected: $($selectedCountry.Name) ($($selectedCountry.Code))" -ForegroundColor Green
    Write-Host ""

    return $selectedCountry.Code
}

# Function to setup GitLab PAT with validation
function Setup-GitLabPAT {
    Write-Progress "Setting up GitLab Personal Access Token for Sourcetree..."

    $gitlabPATUrl = "https://gitlab.com/-/user_settings/personal_access_tokens?page=1&state=active&sort=expires_asc"
    try {
        Start-Process $gitlabPATUrl
    }
    catch {
        Write-FailureAndExit -FailureReason "Cannot open GitLab URL in browser" -Suggestion "Manually navigate to: $gitlabPATUrl" -Phase "GitLab PAT Setup"
    }

    Write-Host ""
    Write-Host "Follow these steps:"
    Write-Host "1. If you don't have a GitLab account, please create one first."
    Write-Host "2. Create a new token. Name it 'Sourcetree' or similar."
    Write-Host "3. Set an expiry date (e.g., 1 year from today, but must be less than 1 year)."
    Write-Host "4. Select scopes: 'api', 'read_repository', 'write_repository'."
    Write-Host "5. Click 'Create personal access token'."
    Write-Host "6. IMPORTANT: Copy the generated token immediately. You will not see it again."
    Write-Host ""
    Write-Host "Now, configure Sourcetree with this token:"
    Write-Host "1. Open Sourcetree."
    Write-Host "2. Go to Tools > Options > Authentication."
    Write-Host "3. Click 'Add'."
    Write-Host "4. Hosting Service: GitLab"
    Write-Host "5. Preferred Protocol: HTTPS"
    Write-Host "6. Click 'Refresh Personal Access Token'."
    Write-Host "7. Username: Your GitLab username (e.g., john.doe, not your email)."
    Write-Host "8. Password: Paste the token you just copied."
    Write-Host "9. Click OK. You should see 'Authentication OK'."

    Read-Host -Prompt "Press any key to continue ONLY AFTER configuring Sourcetree..." | Out-Null
    Write-Success "GitLab PAT setup completed"
}

# Function to setup DWH environment variables with validation
function Setup-DWHVariables {
    param (
        [Parameter(Mandatory = $true)]
        [string]$CountryCode
    )

    Write-Progress "Setting up Data Warehouse environment variables for $CountryCode..."

    $dwhPasswordUrl = "https://auroraenergy.atlassian.net/wiki/spaces/ITHub/pages/3642130481/Data+Warehouse+Password+Update+Methods"
    try {
        Start-Process $dwhPasswordUrl
    }
    catch {
        Write-FailureAndExit -FailureReason "Cannot open DWH password URL" -Suggestion "Manually navigate to: $dwhPasswordUrl" -Phase "DWH Password Retrieval"
    }

    Write-Host ""
    Write-Host "Please access the URL above, log in, and copy the DWH password from the Excel file."

    $dwhPassword = ""
    do {
        $dwhPassword = Read-Host -Prompt "Please paste the DWH password here"
        if ([string]::IsNullOrWhiteSpace($dwhPassword)) {
            Write-Host "Password cannot be empty. Please paste the DWH password." -ForegroundColor Red
        }
    } while ([string]::IsNullOrWhiteSpace($dwhPassword))

    Write-Progress "Setting DWH environment variables for $CountryCode..."

    $years = @("2014", "2019", "2020", "2021", "2022", "2023", "2024")
    foreach ($year in $years) {
        $varName = "DWH_$($CountryCode)_$year"
        try {
            [Environment]::SetEnvironmentVariable($varName, $dwhPassword, "User")
            Write-Success "Set environment variable: $varName"
        }
        catch {
            Write-FailureAndExit -FailureReason "Cannot set environment variable $varName" -Suggestion "Run PowerShell as Administrator or check Windows permissions" -Phase "Environment Variables"
        }
    }

    Write-Success "DWH environment variables configured successfully"
}

# Function to create Aurora API Key with validation
function Create-AuroraAPIKey {
    param (
        [Parameter(Mandatory = $true)]
        [string]$KeysPath
    )

    Write-Progress "Creating Aurora API Key file..."

    $eosApiTokenUrl = "https://eos.auroraer.com/dragonfly/settings/api-tokens"
    try {
        Start-Process $eosApiTokenUrl
    }
    catch {
        Write-FailureAndExit -FailureReason "Cannot open EOS API token URL" -Suggestion "Manually navigate to: $eosApiTokenUrl" -Phase "API Key Creation"
    }

    Write-Host ""
    Write-Host "1. Log in if necessary."
    Write-Host "2. Click 'Create new API token'."
    Write-Host "3. Copy the generated token immediately."

    $apiKey = ""
    do {
        $apiKey = Read-Host -Prompt "Please paste the API token here"
        if ([string]::IsNullOrWhiteSpace($apiKey)) {
            Write-Host "API token cannot be empty. Please paste the token." -ForegroundColor Red
        }
    } while ([string]::IsNullOrWhiteSpace($apiKey))

    $apiKeyFileName = ".aurora-api-key"
    $apiKeyPath = Join-Path $KeysPath $apiKeyFileName

    try {
        $apiKey | Out-File -FilePath $apiKeyPath -Encoding UTF8 -NoNewline
        Write-Success "API key saved to: $apiKeyPath"
    }
    catch {
        Write-FailureAndExit -FailureReason "Cannot save API key to $apiKeyPath" -Suggestion "Check write permissions to C:\Keys directory" -Phase "API Key Creation"
    }
}

# Function for Phase 1 (Pre-restart) with fail-fast
function Execute-Phase1 {
    param (
        [Parameter(Mandatory = $true)]
        [string]$CountryCode,
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath
    )

    Write-Host "################################################################################"
    Write-Host "# Phase 1: Application Installation & Initial Setup                              #"
    Write-Host "################################################################################"
    Write-Host

    # Get country-specific configuration
    $countryConfig = Get-CountryConfig -CountryCode $CountryCode
    $appUrls = Get-ApplicationUrls

    # Filter applications for this country
    $countryApps = @{}
    foreach ($app in $countryConfig.Apps) {
        if ($appUrls.ContainsKey($app)) {
            $countryApps[$app] = $appUrls[$app]
        }
        else {
            Write-FailureAndExit -FailureReason "Application '$app' required for $($countryConfig.Name) but no installation URL configured" -Suggestion "Contact IT support - missing application configuration" -Phase "Application Configuration"
        }
    }

    Write-Host "Required applications for $($countryConfig.Name):"
    $countryApps.Keys | ForEach-Object { Write-Host "  - $_" }
    Write-Host ""

    # Install all required applications (launches Company Portal for missing apps)
    Install-RequiredApplications -AppListWithUrls $countryApps

    # Create directories
    Create-Directories @($PathToKeys, $PathToRepos)

    # Setup GitLab (if Sourcetree is required)
    if ($countryConfig.Apps -contains "Sourcetree") {
        Setup-GitLabPAT
    }

    # Setup DWH environment variables
    Setup-DWHVariables -CountryCode $CountryCode

    # Create Aurora API Key
    Create-AuroraAPIKey -KeysPath $PathToKeys

    # Save state before restart
    Save-ScriptState -Phase "Phase2" -CountryCode $CountryCode -StateFilePath $StateFilePath

    Write-Phase-Complete 1

    # Schedule restart
    Write-Host "################################################################################"
    Write-Host "# RESTART REQUIRED                                                               #"
    Write-Host "################################################################################"
    Write-Host
    Write-Host "A system restart is required for PATH and environment variable changes to take effect."
    Write-Host "After restart, please run this script again to continue with Phase 2."
    Write-Host ""

    $restartChoice = Read-Host -Prompt "Would you like to restart now? (y/n)"

    if ($restartChoice -eq 'y' -or $restartChoice -eq 'Y') {
        Write-Host "System will restart in 60 seconds. You can cancel by closing this window."
        Write-Host "After restart, run this script again to continue setup."

        try {
            Start-Process -FilePath "shutdown.exe" -ArgumentList "/r /t 60 /c 'System restarting for Aurora Onboarding Script - Run script again after restart'" -NoNewWindow
            Read-Host -Prompt "Press any key to continue with restart..." | Out-Null
            exit 0
        }
        catch {
            Write-FailureAndExit -FailureReason "Cannot schedule system restart" -Suggestion "Manually restart your system and run this script again" -Phase "System Restart"
        }
    }
    else {
        Write-Host "Please restart your system and run this script again to continue with Phase 2."
        Read-Host -Prompt "Press any key to exit..." | Out-Null
        exit 0
    }
}

# Function for Phase 2 (Post-restart) with fail-fast
function Execute-Phase2 {
    param (
        [Parameter(Mandatory = $true)]
        [string]$CountryCode,
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath
    )

    Write-Host "################################################################################"
    Write-Host "# Phase 2: Python Environment & Repository Cloning                             #"
    Write-Host "################################################################################"
    Write-Host

    $countryConfig = Get-CountryConfig -CountryCode $CountryCode

    # Setup Python environment (if Python is required)
    if ($countryConfig.Apps -match "Python") {
        Setup-PythonEnvironment -CountryCode $CountryCode
    }

    # Interactively clone repositories (if Git and Sourcetree are required)
    if ($countryConfig.Apps -contains "Git" -and $countryConfig.Apps -contains "Sourcetree") {
        Clone-Repositories-Interactively -TargetPath $PathToRepos
    }

    Write-Phase-Complete 2

    # Continue to Phase 3
    Execute-Phase3 -CountryCode $CountryCode -StateFilePath $StateFilePath
}

# Function to setup Python environment - Simplified
function Setup-PythonEnvironment {
    param (
        [Parameter(Mandatory = $true)]
        [string]$CountryCode
    )

    Write-Progress "Verifying Python installation and PATH configuration..."

    # Verify Python is in PATH
    try {
        $pythonOutput = python --version 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Python command failed with exit code $LASTEXITCODE"
        }
        Write-Success "Python found: $pythonOutput"
    }
    catch {
        Write-FailureAndExit -FailureReason "Python command not found in PATH or not working properly" -Suggestion "Ensure Python was installed with 'Add Python to PATH' selected. You may need to reinstall Python." -Phase "Python Verification"
    }

    Write-Host ""
    Write-Host "IMPORTANT: Disable Python App Execution Aliases to prevent conflicts"
    Write-Host "Opening App Execution Aliases settings..."
    try {
        Start-Process "ms-settings:appexecutionaliases"
    }
    catch {
        Write-FailureAndExit -FailureReason "Cannot open App Execution Aliases settings" -Suggestion "Manually search for 'App execution aliases' in Windows Settings and disable python.exe entries" -Phase "Python Configuration"
    }

    Write-Host ""
    Write-Host "In the settings window:"
    Write-Host "Turn OFF any entries for 'python.exe' and 'python3.exe'."
    Read-Host -Prompt "Press any key ONLY AFTER disabling App Execution Aliases..." | Out-Null
    
    Write-Success "Python environment verification completed."
}

# Fixed function to interactively clone repositories using the blobless method
function Clone-Repositories-Interactively {
    param (
        [Parameter(Mandatory = $true)]
        [string]$TargetPath
    )

    Write-Progress "Starting interactive repository cloning..."

    # Test Git is working
    try {
        git --version | Out-Null
        if ($LASTEXITCODE -ne 0) { throw "Git command failed" }
    }
    catch {
        Write-FailureAndExit -FailureReason "Git is not working properly" -Suggestion "Ensure Git is installed and in PATH. You may need to restart after Git installation." -Phase "Git Verification"
    }

    # Ensure target directory exists
    if (-not (Test-Path $TargetPath)) {
        Create-Directories -Directories @($TargetPath)
    }
    Set-Location $TargetPath

    # Prompt for GitLab credentials once for the session
    Write-Host ""
    Write-Host "GitLab Command-Line Authentication Required" -ForegroundColor Cyan
    Write-Host "Please enter your GitLab credentials. This will be used for all cloning operations in this session."
    Write-Host "NOTE: This is separate from the Sourcetree authentication."
    Write-Host ""
    
    $gitlabUsername = ""
    do {
        $gitlabUsername = Read-Host -Prompt "GitLab Username (e.g., john.doe, not your email)"
        if ([string]::IsNullOrWhiteSpace($gitlabUsername)) {
            Write-Host "Username cannot be empty" -ForegroundColor Red
        }
    } while ([string]::IsNullOrWhiteSpace($gitlabUsername))

    # Get the token and handle it properly regardless of type
    $gitlabTokenInput = Read-Host -Prompt "GitLab Personal Access Token (PAT) (Right-click to paste)" -AsSecureString
    
    # Check if we actually got a SecureString or a regular string
    $isSecureString = $gitlabTokenInput -is [System.Security.SecureString]
    
    if ($isSecureString) {
        if ($gitlabTokenInput.Length -eq 0) {
            Write-Host "Token was empty. Skipping repository cloning." -ForegroundColor Yellow
            return
        }
    } else {
        # It's a regular string, check if it's empty
        if ([string]::IsNullOrWhiteSpace($gitlabTokenInput)) {
            Write-Host "Token was empty. Skipping repository cloning." -ForegroundColor Yellow
            return
        }
    }

    $bstr = [System.IntPtr]::Zero
    $plainToken = $null
    
    try {
        if ($isSecureString) {
            # Convert SecureString to plain text for use in this block
            $bstr = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($gitlabTokenInput)
            $plainToken = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($bstr)
        } else {
            # It's already a plain string
            $plainToken = $gitlabTokenInput
        }

        while ($true) {
            Write-Host ""
            Write-Host "Enter the full HTTPS URL of the repository you want to clone." -ForegroundColor Cyan
            Write-Host "The URL should end in '.git'. (Right-click to paste)" -ForegroundColor Cyan
            $repoUrl = Read-Host -Prompt "Or type 'skip' to finish cloning"

            if ($repoUrl -eq 'skip') {
                Write-Host "Finishing repository cloning." -ForegroundColor Yellow
                break
            }

            if (-not ($repoUrl -like "*.git")) {
                Write-Host "Invalid URL. Please provide a valid HTTPS URL ending in .git" -ForegroundColor Red
                continue
            }

            # Extract repo name from URL to create the folder
            try {
                $repoName = ($repoUrl.Split('/')[-1]).Replace(".git", "")
                $repoPath = Join-Path $TargetPath $repoName
            }
            catch {
                Write-Host "Could not determine repository name from URL. Please check the URL and try again." -ForegroundColor Red
                continue
            }

            if (Test-Path $repoPath) {
                Write-Host "Directory '$repoName' already exists in $TargetPath. Skipping." -ForegroundColor Yellow
                continue
            }

            Write-Progress "Cloning $repoName using lightweight method..."
            try {
                # Create authenticated URL for the clone operation
                $authenticatedUrl = $repoUrl -replace "https://", "https://$($gitlabUsername):$($plainToken)@"

                # 1. Clone with depth 1 and no blobs, using the authenticated URL
                git clone --depth=1 --filter=blob:none $authenticatedUrl $repoPath
                if ($LASTEXITCODE -ne 0) { throw "Git clone failed. Check credentials and URL." }
                Write-Success "Initial clone of $repoName successful."

                # 2. Navigate into the new directory
                Set-Location $repoPath
                Write-Progress "Inside $repoName, configuring for full history fetch..."

                # 3. Reset the remote URL to the clean, non-authenticated version for security
                git remote set-url origin $repoUrl
                if ($LASTEXITCODE -ne 0) { throw "Failed to reset remote URL." }

                # 4. Configure fetch to get all branches
                git config remote.origin.fetch "+refs/heads/*:refs/remotes/origin/*"
                if ($LASTEXITCODE -ne 0) { throw "Git config failed." }

                # 5. Fetch the full history
                Write-Progress "Fetching full history (unshallowing)... This may take a moment."
                git fetch origin --unshallow
                if ($LASTEXITCODE -ne 0) { Write-Host "Warning: Git unshallow fetch failed, but the initial clone was successful." -ForegroundColor Yellow }

                Write-Success "Successfully cloned and fetched full history for $repoName."
                Write-Host ""
                Write-Host "ACTION REQUIRED:" -ForegroundColor Yellow
                Write-Host "Please add the repository to Sourcetree now." -ForegroundColor Cyan
                Write-Host "  1. Open Sourcetree"
                Write-Host "  2. Click File > Open, or drag the folder onto the Sourcetree window."
                Write-Host "  3. The folder path is: $repoPath" -ForegroundColor White
                Read-Host -Prompt "Press any key to continue to the next repository..." | Out-Null

                # Return to the parent Repositories directory
                Set-Location $TargetPath
            }
            catch {
                Write-Host "An error occurred while cloning ${repoName}: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "Please check the URL and your GitLab permissions." -ForegroundColor Yellow
                # Return to parent directory in case of failure
                Set-Location $TargetPath
            }
        }
    }
    finally {
        # Securely clear the token from memory
        if ($bstr -ne [System.IntPtr]::Zero) {
            [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($bstr)
        }
        if ($plainToken) {
            # Clear the plain text token variable
            $plainToken = $null
        }
        # Only dispose if it's actually a SecureString
        if ($isSecureString -and $gitlabTokenInput) {
            try {
                $gitlabTokenInput.Dispose()
            }
            catch {
                # Ignore disposal errors - the token might already be disposed
            }
        }
    }

    Write-Success "Interactive repository cloning finished."
}

# Function for Phase 3 (Application Configuration) with fail-fast
function Execute-Phase3 {
    param (
        [Parameter(Mandatory = $true)]
        [string]$CountryCode,
        [Parameter(Mandatory = $true)]
        [string]$StateFilePath
    )

    Write-Host "################################################################################"
    Write-Host "# Phase 3: Application Configuration                                           #"
    Write-Host "################################################################################"
    Write-Host

    $countryConfig = Get-CountryConfig -CountryCode $CountryCode

    # Setup SQL Workbench guidance (if required)
    if ($countryConfig.Apps -contains "SQLWorkbench") {
        Setup-SQLWorkbench
    }

    # Setup ODBC (if Redshift is required)
    if ($countryConfig.Apps -contains "Redshift") {
        Setup-ODBC -CountryCode $CountryCode
    }

    # Setup PuTTY and WinSCP (if required)
    if ($countryConfig.Apps -contains "PuTTY" -and $countryConfig.Apps -contains "WinSCP") {
        Setup-PuTTYAndWinSCP -KeysPath $PathToKeys -CountryConfig $countryConfig
    }

    # NOTE: QGIS installation is now handled in Phase 1 and no longer needs a separate step here.

    Write-Phase-Complete 3

    # Final phase
    Execute-FinalPhase -CountryCode $CountryCode -StateFilePath $StateFilePath
}

# Configuration functions (guidance only - no fail-fast needed)
function Setup-SQLWorkbench {
    Write-Host "SQL Workbench Configuration Guidance:"
    Write-Host "1. Ensure you downloaded the AER SQL Workbench folder from Company Portal."
    Write-Host "2. Extract it to C:\SQLWorkbench_AER (recommended)."
    Write-Host "3. Run aer_setup.bat inside the folder."
    Write-Host "4. Launch SQLWorkbench64.exe from that folder."
    Write-Host ""
    Write-Host "When setting up a connection profile for the Data Warehouse:"
    Write-Host "    Username: audcurrency2024_production"
    Write-Host "    Password: Use the DWH password you obtained earlier."
    Write-Host "    Ensure you are connected to the VPN if required for access."
    Read-Host -Prompt "Press any key after reviewing SQL Workbench setup notes..." | Out-Null
}

function Setup-ODBC {
    param ([string]$CountryCode)

    Write-Host "Amazon Redshift ODBC Driver Configuration Guidance:"
    Write-Host "Opening ODBC Data Sources (64-bit)..."
    try {
        Start-Process "odbcad32.exe"
    }
    catch {
        Write-Host "Could not open ODBC automatically. Please search for 'ODBC Data Sources (64-bit)' in Start Menu" -ForegroundColor Yellow
    }

    Write-Host ""
    Write-Host "Configure the Redshift connection:"
    Write-Host "    Data Source Name: Redshift"
    Write-Host "    Server: warehouse.chgkrthnr8sn.eu-west-2.redshift.amazonaws.com"
    Write-Host "    Port: 5439"
    Write-Host "    Database: production"
    Write-Host "    User: audcurrency2024_production"
    Write-Host "    Password: Use the DWH password from earlier"

    Read-Host -Prompt "Press any key after configuring ODBC..." | Out-Null
}

function Setup-PuTTYAndWinSCP {
    param ([string]$KeysPath, [hashtable]$CountryConfig)

    $keyFilePath = Join-Path $KeysPath $CountryConfig.PuttyKeyFile

    Write-Host "PuTTY & WinSCP Configuration:"
    Write-Host "Required key file: $keyFilePath"

    if (-not (Test-Path $keyFilePath)) {
        Write-Host "Key file not found. Please obtain $($CountryConfig.PuttyKeyFile) and place it in C:\Keys" -ForegroundColor Yellow
    }

    Write-Host "Configure PuTTY session 'AWS_large' with:"
    Write-Host "    Host: <EMAIL>"
    Write-Host "    Port: 22, SSH"
    Write-Host "    Auth key: $keyFilePath"

    Read-Host -Prompt "Press any key after reviewing PuTTY/WinSCP setup..." | Out-Null
}

# Function for Final Phase
function Execute-FinalPhase {
    param ([string]$CountryCode, [string]$StateFilePath)

    Write-Host "################################################################################"
    Write-Host "# Setup Complete - Welcome to Aurora Energy Research!                          #" -ForegroundColor Green
    Write-Host "################################################################################"
    Write-Host

    $countryConfig = Get-CountryConfig -CountryCode $CountryCode

    Write-Success "Onboarding completed successfully for $($countryConfig.Name) ($CountryCode)!"
    Write-Host ""
    Write-Host "Final checklist:"
    Write-Host "✅ Applications installed and verified"
    Write-Host "✅ GitLab access configured (if applicable)"
    Write-Host "✅ Environment variables set"
    Write-Host "✅ API key created"
    Write-Host "✅ Python environment configured (if applicable)"
    Write-Host "✅ Repositories cloned (if applicable)"
    Write-Host "✅ Applications configured"

    # Clean up state file
    if (Test-Path $StateFilePath) {
        Remove-Item $StateFilePath -Force -ErrorAction SilentlyContinue
    }

    Write-Host ""
    Write-Host "You're all set! If you encounter any issues, please contact IT support."
    Read-Host -Prompt "Press any key to exit..." | Out-Null
}

# Main script execution
Write-Host "###################################################################"
Write-Host "# Welcome to the Aurora Energy Research New Joiner Setup Script!  #" -ForegroundColor Cyan
Write-Host "# This script will guide you through setting up your environment. #"
Write-Host "# If any step fails, the script will stop and show you why.       #"
Write-Host "###################################################################"
Write-Host
Read-Host -Prompt "Press any key to continue..." | Out-Null
Write-Host

# Global variables
# Define required types globally to avoid re-definition errors in loops
if (-not ([System.Management.Automation.PSTypeName]'Win32').Type) {
    Add-Type -TypeDefinition @"
using System;
using System.Runtime.InteropServices;
public class Win32 {
    [DllImport("user32.dll")]
    public static extern bool SetForegroundWindow(IntPtr hWnd);
    [DllImport("user32.dll")]
    public static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);
}
"@
}

if (-not ([System.Management.Automation.PSTypeName]'System.Windows.Forms.SendKeys').Type) {
    Add-Type -AssemblyName System.Windows.Forms
}

$PathToKeys = "C:\Keys"
$PathToRepos = "C:\Repositories"
$StateFilePath = Join-Path $env:TEMP "aurora_onboarding_state.json"

# Check if we're resuming from a previous run
$savedState = Load-ScriptState -StateFilePath $StateFilePath

if ($savedState) {
    Write-Host "Previous setup detected for $($savedState.CountryCode)."
    Write-Host "Last completed: $($savedState.Phase) at $($savedState.Timestamp)"
    Write-Host ""

    $resumeChoice = Read-Host -Prompt "Would you like to resume from where you left off? (y/n)"

    if ($resumeChoice -eq 'y' -or $resumeChoice -eq 'Y') {
        $selectedCountry = $savedState.CountryCode

        # Resume from the appropriate phase
        switch ($savedState.Phase) {
            "Phase2" {
                Write-Host "Resuming from Phase 2..." -ForegroundColor Green
                Execute-Phase2 -CountryCode $selectedCountry -StateFilePath $StateFilePath
            }
            "Phase3" {
                Write-Host "Resuming from Phase 3..." -ForegroundColor Green
                Execute-Phase3 -CountryCode $selectedCountry -StateFilePath $StateFilePath
            }
            default {
                Write-Host "Unknown phase. Starting from the beginning..." -ForegroundColor Yellow
                $selectedCountry = Select-Country
                Execute-Phase1 -CountryCode $selectedCountry -StateFilePath $StateFilePath
            }
        }
        exit 0
    }
    else {
        Write-Host "Starting fresh setup..."
        Remove-Item $StateFilePath -Force -ErrorAction SilentlyContinue
    }
}

# Fresh start - select country and begin Phase 1
$selectedCountry = Select-Country
Execute-Phase1 -CountryCode $selectedCountry -StateFilePath $StateFilePath