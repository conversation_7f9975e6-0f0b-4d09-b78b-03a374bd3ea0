﻿
Clear-Host

# function to remove colon after Read-Host
function Read-Host($prompt) {
  Write-Host "$prompt  " -NoNewline
  Microsoft.PowerShell.Utility\Read-Host }


# Function to write phase complete 
function Write-Phase-Complete{
    param (
        [Parameter(Mandatory=$true)]
        $phase
    )

    Write-Host
    Write-Host "################################################################################"
    Write-Host "# Phase $phase Complete.                                                            #"
    Write-Host "################################################################################"
    Write-Host
    Read-Host -Prompt "Press any key to continue..." | Out-Null
    Write-Host
}

Write-Host "###################################################################"
Write-Host "# Welcome to the Aurora Energy Research New Joiner Setup Script!  #"
Write-Host "# This script will guide you through setting up your environment. #"
Write-Host "# Please follow the prompts carefully.                            #"
Write-Host "###################################################################"
Write-Host 
Read-Host -Prompt "Press any key to continue..." | Out-Null
Write-Host 
Write-Host "Creating essential directories if they don't exist..."
Write-Host 

# Create folders required for secrets


function Create-Directories{
    param (
        [Parameter(Mandatory=$true)]
        $Directories
    )

    foreach($path in $Directories)
    {
        If(!(test-path -PathType container $path))
        {
            Write-Warning "Directory '$path' not found. Creating directory."
            Write-Host
            New-Item -ItemType Directory -Path $path | Out-Null
        }
        Else
        {
            Write-Host "Directory '$path' exists."
            Write-Host 
        }
    }

}

$PathToKeys="C:\Keys"
$PathToRepos="C:\Repositories_3"


Create-Directories @($PathToKeys, "$PathToRepos")


function Find-AndLaunchAppOrCompanyPortal {
    param (
        [Parameter(Mandatory=$true)]
        [hashtable]$AppListWithUrls
    )

    Write-Host "Looking for applications installed in Windows..."

    # Retrieve lists of all installed applications once for efficiency
    # Filtering out common Microsoft/Windows built-in apps for AppX packages
    $allModernApps = Get-AppxPackage | Where-Object { -not ($_.Name -like "Microsoft.*" -or $_.Publisher -like "CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US") } | Select-Object -ExpandProperty Name

    # Get traditional apps from various registry paths
    # Ensure DisplayName exists before selecting it to avoid errors
    $allTraditionalAppsHKLM = Get-ItemProperty HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\* | Where-Object { $_.DisplayName -ne $null } | Select-Object -ExpandProperty DisplayName
    $allTraditionalAppsWow64 = Get-ItemProperty HKLM:\Software\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\* | Where-Object { $_.DisplayName -ne $null } | Select-Object -ExpandProperty DisplayName
    $allTraditionalAppsHKCU = Get-ItemProperty HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\* | Where-Object { $_.DisplayName -ne $null } | Select-Object -ExpandProperty DisplayName

    # Combine all traditional app display names into a single array for easier searching
    $allTraditionalApps = ($allTraditionalAppsHKLM + $allTraditionalAppsWow64 + $allTraditionalAppsHKCU) | Sort-Object -Unique

    foreach ($appName in $AppListWithUrls.Keys) {
        $companyPortalUrl = $AppListWithUrls[$appName]
        $appFound = $false

        Write-Host "`nSearching for: '$appName'..."

        # Check if the app exists in modern (AppX) applications
        if ($allModernApps -like "*$appName*") {
            Write-Host "  Found modern app matching '$appName'."
            # Corrected: Use $true for boolean literal
            $appFound = $true
        }
        # Check if the app exists in traditional applications
        elseif ($allTraditionalApps -like "*$appName*") {
            Write-Host "  Found traditional app matching '$appName'."
            # Corrected: Use $true for boolean literal
            $appFound = $true
        }

        # If the application is not found after checking all locations,
        # launch the Company Portal URL and stop the script.
        if (-not $appFound) {
            [System.Windows.MessageBox]::Show("App '$appName' not found. Launching Company Portal.")
            #TODO specify if URL external or company portal
            Write-Warning "App '$appName' not found. Launching Company Portal URL: $companyPortalUrl - Exiting now"
            Start-Process $companyPortalUrl
            exit # Stop the script immediately as per requirement
        }
    }

    Write-Host "`n-------------------------------------------------"
    Write-Host "All specified applications found or handled successfully."
}

# Required packages defined as an [ordered] hashtable where keys are application names (or parts of names)
# and values are their corresponding Company Portal URLs.
# Using [ordered] ensures that the applications are processed in the order they are defined.
# If an app is not found, its URL will be launched and the script will exit.
$AppsToVerify = [ordered]@{
    "Sourcetree" = "companyportal:ApplicationId=da4ceb74-9881-43d6-8b43-7ddd863e9e00"
    "Python 3.8" = "companyportal:ApplicationId=f3cc6d67-95bf-446e-9164-62dde8ce6eca"
    "Python 3.12" = "companyportal:ApplicationId=d4e36704-2c6c-4b69-83c6-5eb525c00d3f"
    "PyCharm" = "companyportal:ApplicationId=c0d06534-4326-48ed-9370-05a48a6ac046"
    "Putty" = "companyportal:ApplicationId=b55b70c3-c3b5-4b69-998a-7e313829fb67"
    "WinSCP" = "companyportal:ApplicationId=40c72b1d-a9aa-40f8-a3b9-620e0e77871d"
    "Redshift" = "companyportal:ApplicationId=9515084f-bee7-4e86-becc-d74e025c291b"
    "R for Windows" = "companyportal:ApplicationId=9d11d46b-d144-489a-883b-6654a1d94f05"    
    "RStudio" = "companyportal:ApplicationId=a60814af-6742-4415-ace7-1ca88b8f011e"
    "Git" = "companyportal:ApplicationId=046d5e09-de15-46cc-9382-ca0b385c4f9d"
    "Node.js" = "https://nodejs.org/en/download"
    "Java" = "https://www.java.com/en/download/manual.jsp"
}

# Call the function, passing the hashtable of applications to check.
#Find-AndLaunchAppOrCompanyPortal -AppListWithUrls $AppsToVerify

Write-Phase-Complete 1

# TODO - write docs on Gitlab and SSH (can't be automated)

function Clone-Repositories {
    param (
        [Parameter(Mandatory=$true)]
        [hashtable]$RepositoryListWithUrls
    )
    Write-Host "Cloning repositories from Gitlab"
    Write-Host "Make sure your configuration is correct"
    
    # Delete all folders and subfolders on the repository directory
    Remove-Item $PathToRepos -Recurse -Force

    foreach ($repoName in $RepositoryListWithUrls.Keys) {
        $gitRemoteURL = $RepositoryListWithUrls[$repoName]
        $repoPath = Join-Path "$PathToRepos" -ChildPath "$repoName"
        Write-Host "Cloning $gitRemoteURL into $repoPath"
        Invoke-Expression 'git clone --depth=1 --filter=blob:none $gitRemoteURL $repoPath' 2> $null
       }
}

$ReposToClone = [ordered]@{
    "automatingadvisoryoutputs" = "**************:aermod/analyst_users/automatingadvisoryoutputs.git"
    "aeren-line-rating" = "**************:aermod/analyst_users/aeren-line-rating.git"
    "one-stop-shop" = "**************:aermod/analyst_users/one-stop-shop.git"
    "chronos-inputs" = "**************:aermod/chronos_input/aus.git"
    "aeren-inputs" = "**************:aermod/aer-en_inputs.git"
    "aeres-inputs" = "**************:aermod/DE/**********************.git"
}

function Find-QGIS {
    $commonPaths = @(
    "C:\Program Files\QGIS*",
    "C:\OSGeo4W64\bin\*",
    "C:\OSGeo4W\bin\*",
    "C:\OSGeo4W\bin\*",
    "$env:LOCALAPPDATA\Programs\QGIS*"
    )

    foreach ($path in $commonPaths) {
        $found = Get-ChildItem -Path $path -ErrorAction SilentlyContinue
        if ($found) {
            Write-Host "QGIS found"
            $qgisFound = $true
            break
        }
    }

    if (-not $qgisFound) {
        Write-Host "QGIS not found. Launching installer"
        Start-Process https://qgis.org/download/
    }
}

Write-Host "Cloning repos to the target directory"
Clone-Repositories -RepositoryListWithUrls $ReposToClone
Write-Phase-Complete 2


Write-Host "Setting up Data Warehouse environment variables"

Write-Phase-Complete 3


Write-Host "Installing Daff globally using npm"
#npm install daff -g
Write-Phase-Complete 4

Write-Host "Creating Aurora API Key"
Write-Phase-Complete 5

#key_path = Path("C:/Keys") / AURORA_API_KEY_FILE_NAME
#with open(key_path, "w") as f:
#    f.write(key.strip())
#AURORA_API_KEY_FILE_NAME = ".aurora-api-key"

# TODO SQL workbench install
Write-Host "Installing SQL Workbench"
Write-Phase-Complete 6

#"https://auroraer.sharepoint.com/:f:/r/Tools/Tools/Onboarding/SQLWorkbench?csf=1&web=1&e=4IBs5s"

# Get Athena PowerBI - can't be automated / needs to be placed where user prefers

Write-Host "Installing QGIS"
#Find-QGIS
Write-Phase-Complete 7


# https://auroraenergy.atlassian.net/wiki/spaces/AW/pages/3328573571/Installing+AUS+QGIS+Dashboards / download to C:/QGIS
# - download dashboard
# Autofind

#https://qgis.org/download/

# Putty & WinSCP

# https://auroraer.sharepoint.com/:x:/r/_layouts/15/Doc.aspx?sourcedoc=%7B4F7612D0-ABDD-49F3-9998-23C1DF48D81C%7D&file=Data%20Warehouse%20Credentials.xlsx&nav=MTVfezAwMDAwMDAwLTAwMDEtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMH0&action=default&mobileredirect=true